# 1. 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置绘图风格（可选，让图更美观）
plt.style.use('seaborn-v0_8-whitegrid') # 使用带网格的清爽风格
sns.set_palette("Set2") # 设置颜色盘
plt.rcParams['font.sans-serif'] = ['SimHei'] # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False # 用来正常显示负号

# 2. 加载数据
df = pd.read_csv('NIPT_data.csv') # 如果是Excel文件，使用 pd.read_excel('NIPT_data.xlsx')

# 3. 筛选出男胎数据（因为问题1是针对Y染色体）
# 假设有一列叫做 'fetal_sex'，其中 'Male' 代表男胎。请根据你的数据实际列名修改。
male_df = df[df['fetal_sex'] == 'Male'].copy()

# 查看数据基本信息，确认筛选成功
print(male_df.info())
print(male_df[['gestational_week', 'BMI', 'Y_chrom']].head()) # 请将'Y_chrom'等替换为你的实际列名

# 创建一个大画布，包含两个子图
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# 子图1：Y染色体浓度 vs. 孕周
ax1.scatter(male_df['gestational_week'], male_df['Y_chrom'], alpha=0.6) # alpha设置点透明度，防止重叠点看不清
ax1.set_xlabel('Gestational Week (周)')
ax1.set_ylabel('Y Chromosome Concentration (%)')
ax1.set_title('Y染色体浓度 vs. 孕周')
# 可以添加一条趋势线更直观地看相关性的方向
z1 = np.polyfit(male_df['gestational_week'], male_df['Y_chrom'], 1) # 1次线性拟合
p1 = np.poly1d(z1)
ax1.plot(male_df['gestational_week'], p1(male_df['gestational_week']), "r--", lw=1) # 绘制红色趋势线
# 在图上显示相关系数R和方程
correlation_week = male_df['Y_chrom'].corr(male_df['gestational_week'])
equation_text = f'y = {z1[0]:.3f}x + {z1[1]:.3f}\nR = {correlation_week:.3f}'
ax1.text(0.05, 0.95, equation_text, transform=ax1.transAxes, fontsize=10, verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

# 子图2：Y染色体浓度 vs. BMI
ax2.scatter(male_df['BMI'], male_df['Y_chrom'], alpha=0.6)
ax2.set_xlabel('BMI')
ax2.set_ylabel('Y Chromosome Concentration (%)')
ax2.set_title('Y染色体浓度 vs. BMI')
# 同样添加趋势线
z2 = np.polyfit(male_df['BMI'], male_df['Y_chrom'], 1)
p2 = np.poly1d(z2)
ax2.plot(male_df['BMI'], p2(male_df['BMI']), "r--", lw=1)
correlation_bmi = male_df['Y_chrom'].corr(male_df['BMI'])
equation_text = f'y = {z2[0]:.3f}x + {z2[1]:.3f}\nR = {correlation_bmi:.3f}'
ax2.text(0.05, 0.95, equation_text, transform=ax2.transAxes, fontsize=10, verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

# 自动调整布局并显示图形
plt.tight_layout()
plt.show()